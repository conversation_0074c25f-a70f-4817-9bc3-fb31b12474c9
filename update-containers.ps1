# Docker Container Update Script for Abacus
# This script provides manual control over container updates

param(
    [string[]]$Services = @(),
    [switch]$All,
    [switch]$Safe,
    [switch]$DryRun,
    [switch]$Help
)

# Define service categories
$SafeServices = @(
    "open-webui",
    "n8n", 
    "librechat",
    "searxng",
    "app",  # perplexica
    "flowise",
    "crawl4ai",
    "agent-zero",
    "mcpo",
    "tailscale"
)

$DatabaseServices = @(
    "abacus_chat_mongodb",
    "abacus_chat_meilisearch", 
    "librechat_vectordb",
    "nextclouddb",
    "redis"
)

$CustomBuildServices = @(
    "whisperx",
    "vllm"
)

function Show-Help {
    Write-Host "Docker Container Update Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\update-containers.ps1 -Services service1,service2"
    Write-Host "  .\update-containers.ps1 -Safe                    # Update only safe services"
    Write-Host "  .\update-containers.ps1 -All                     # Update all services (dangerous)"
    Write-Host "  .\update-containers.ps1 -DryRun -Safe            # Show what would be updated"
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "  -Services    Comma-separated list of services to update"
    Write-Host "  -Safe        Update only safe services (recommended)"
    Write-Host "  -All         Update all services (includes databases - use with caution)"
    Write-Host "  -DryRun      Show what would be updated without actually updating"
    Write-Host "  -Help        Show this help message"
    Write-Host ""
    Write-Host "Safe Services:" -ForegroundColor Green
    $SafeServices | ForEach-Object { Write-Host "  - $_" }
    Write-Host ""
    Write-Host "Database Services (not recommended for auto-update):" -ForegroundColor Red
    $DatabaseServices | ForEach-Object { Write-Host "  - $_" }
    Write-Host ""
    Write-Host "Custom Build Services (require rebuild):" -ForegroundColor Yellow
    $CustomBuildServices | ForEach-Object { Write-Host "  - $_" }
}

function Update-Service {
    param([string]$ServiceName)
    
    Write-Host "Updating $ServiceName..." -ForegroundColor Green
    
    if ($DryRun) {
        Write-Host "[DRY RUN] Would update: $ServiceName" -ForegroundColor Yellow
        return
    }
    
    try {
        # Check if service exists
        $serviceExists = docker-compose ps --services | Where-Object { $_ -eq $ServiceName }
        if (-not $serviceExists) {
            Write-Host "Service '$ServiceName' not found in docker-compose.yaml" -ForegroundColor Red
            return
        }
        
        # Stop the service
        Write-Host "  Stopping $ServiceName..."
        docker-compose stop $ServiceName
        
        if ($CustomBuildServices -contains $ServiceName) {
            # For custom build services, rebuild
            Write-Host "  Rebuilding $ServiceName (custom build)..."
            docker-compose build --no-cache $ServiceName
        } else {
            # Pull latest image
            Write-Host "  Pulling latest image for $ServiceName..."
            docker-compose pull $ServiceName
        }
        
        # Start the service
        Write-Host "  Starting $ServiceName..."
        docker-compose up -d $ServiceName
        
        # Wait a moment and check health
        Start-Sleep -Seconds 5
        $status = docker-compose ps $ServiceName --format "table {{.State}}"
        Write-Host "  Status: $status" -ForegroundColor Cyan
        
        Write-Host "$ServiceName updated successfully!" -ForegroundColor Green
        Write-Host ""
        
    } catch {
        Write-Host "Error updating $ServiceName`: $_" -ForegroundColor Red
    }
}

function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    # Determine which services to update
    $ServicesToUpdate = @()
    
    if ($All) {
        # Get all services from docker-compose
        $ServicesToUpdate = docker-compose config --services
        Write-Host "Updating ALL services (including databases)..." -ForegroundColor Red
        Write-Host "This includes database services which may cause data issues!" -ForegroundColor Red
        $confirm = Read-Host "Are you sure? Type 'yes' to continue"
        if ($confirm -ne "yes") {
            Write-Host "Cancelled." -ForegroundColor Yellow
            return
        }
    } elseif ($Safe) {
        $ServicesToUpdate = $SafeServices
        Write-Host "Updating safe services only..." -ForegroundColor Green
    } elseif ($Services.Count -gt 0) {
        $ServicesToUpdate = $Services
        Write-Host "Updating specified services: $($Services -join ', ')" -ForegroundColor Cyan
    } else {
        Write-Host "No services specified. Use -Help for usage information." -ForegroundColor Yellow
        return
    }
    
    if ($DryRun) {
        Write-Host "DRY RUN MODE - No actual changes will be made" -ForegroundColor Yellow
        Write-Host ""
    }
    
    # Warn about database services
    $dbServicesInList = $ServicesToUpdate | Where-Object { $DatabaseServices -contains $_ }
    if ($dbServicesInList.Count -gt 0) {
        Write-Host "WARNING: The following database services are included:" -ForegroundColor Red
        $dbServicesInList | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
        Write-Host "Updating database services can cause data corruption or compatibility issues!" -ForegroundColor Red
        if (-not $DryRun) {
            $confirm = Read-Host "Continue anyway? Type 'yes' to proceed"
            if ($confirm -ne "yes") {
                Write-Host "Cancelled." -ForegroundColor Yellow
                return
            }
        }
    }
    
    # Update each service
    foreach ($service in $ServicesToUpdate) {
        Update-Service -ServiceName $service
    }
    
    if (-not $DryRun) {
        # Clean up unused images
        Write-Host "Cleaning up unused images..." -ForegroundColor Cyan
        docker image prune -f
        
        Write-Host ""
        Write-Host "Update process completed!" -ForegroundColor Green
        Write-Host "Check logs with: docker-compose logs -f [service-name]" -ForegroundColor Cyan
    }
}

# Run the main function
Main
