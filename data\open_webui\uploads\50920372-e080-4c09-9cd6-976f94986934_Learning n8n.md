---
tags: [Learning, n8n, AI, Resource, Reference]
---

# How n8n functions

*Learned: 2025-02-21 | Source: Documentation and tutorials*

## Takeaway
- n8n is an AI agent building platform
- n8n seems to have a easy learning curve

## Details
- Learning how to incorporate Gmail API into n8n
- AI Agent fetches latest email, extracts the body, decides based on my rules, assigns label to email
- marks each email with an additional "ai-agent/checked" label

## Source
- [Google oAuth services](https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/?utm_source=n8n_app&utm_medium=credential_settings&utm_campaign=create_new_credentials_modal#configure-your-oauth-consent-screen)

## Why It Matters
- Can start learning how to build tailored AI agents
- Will help with my [[AI Auto Gmail Labler]]

## Next Steps
- possibly make an unsure label nested under "ai-agent/checked"
- Continue exploring n8n workflows and integrations

## Connections
- [[AI Auto Gmail Labler]] - Direct application
- [[Building AI Agents with n8n]] - Related project
- [[AI Agent Obsidian Note]] - n8n workflow implementation
- [[AI Agent Architect System Prompt]] - Agent design principles

## Related Topics
- [[AI's Role in Replacing Services and Personal Organization]] - Automation philosophy
- Workflow automation
- API integrations
- Email processing
- AI agent development
- No-code platforms
