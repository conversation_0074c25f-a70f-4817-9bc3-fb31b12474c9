---
tags: [AI, System, Abacus, Agents, A2A, Project, Framework]
---

# Abacus AI System - Specification & Build Instructions

## 🧠 Project Overview
A distributed AI agent framework built around one main "dispatcher" agent (Abacus) that breaks down user tasks and delegates them to specialized sub-agents via the A2A protocol. Inspired by systems like GenSpark and Manus AI, but with a modular, plug-and-play design and a futuristic chat interface.

## 🧱 Core Components

### ✅ Abacus (Main Agent)
- Accepts natural language user commands
- Parses intent + breaks into subtasks
- Queries available agents via A2A discovery
- Delegates tasks to sub-agents via A2A
- Tracks user preferences + agent performance

### ✅ Sub-Agents
Each agent:
- Exposes an `/agent-card` endpoint
- Accepts A2A task requests via POST
- Performs tool-based tasks via MCP or local functions
- Returns structured JSON results
- Reasoning powered by local LLMs (via Ollama, vLLM, etc.)
- Includes `visual_role` key for agent UI mapping (`core`, `nlp`, `api`, etc.)

Example agents: `SpotifyAgent`, `TextAgent`, `PDFMaker`, `WebSearchAgent`, etc.

## 🌐 Communication Layer: A2A Protocol

### ✅ Agent Discovery
Abacus scans known agents or a registry, pulls their `/agent-card`, and builds a live capability map.

### ✅ Agent Card Format
```json
{
  "id": "spotify-agent",
  "name": "SpotifyAgent",
  "description": "Handles Spotify tasks like creating playlists and searching tracks.",
  "skills": ["create_playlist", "search_music"],
  "examples": ["Play chill music", "Create a workout playlist"],
  "a2a_endpoint": "http://localhost:9001/a2a",
  "icon": "fas fa-music",
  "color": "text-purple-400",
  "status": "online",
  "visual_role": "api",
  "version": "1.0.0"
}
```

## 💬 User Interface (Web)
- Futuristic themed desktop layout
- Features:
  - Capabilities Panel (auto-generated from agent-card examples)
  - Connected Services Panel (lists currently online agents)
  - Chat Interface (primary conversation area)
  - Task Progress Panel (live updates for running agents)
  - Agent System Visualization (radial layout with data pulses based on `visual_role`)
  - Command History (populated from completed tasks)
  - System Status Panel (tracks Abacus + agent health)
  - Voice Mode (toggle to use browser speech-to-text input)

## 🔁 Task Types: Quick vs Deep Search

### ✅ Quick Search
- Trigger: "just look this up", "quick search"
- Route: WebSearchAgent → SummarizerAgent → Reply

### ✅ Deep Search Workflow
- Trigger: "deep search", "thorough research"
- Flow:
  1. WebSearchAgent (initial query)
  2. SummarizerAgent (summarize each source)
  3. FactCheckerAgent (validate info)
  4. GapFinderAgent (find missing pieces)
  5. SearchRefinerAgent (improve the query)
  6. ReportWriterAgent (compile final report)
- Visualized as a radial animation
- Abacus always serves as orchestrator

## 🧠 Memory Design

### ✅ Abacus Memory
- Knows global user preferences
- Determines best agents + tone

### ✅ Per-Agent Memory
- Each agent stores user-specific preferences (e.g., preferred genre for Spotify)
- Divided into short-term (per session) and long-term (persistent) memory
- Accessible via the UI in a Settings > Agent Memory panel
- Users can:
  - View memory per agent
  - Edit stored values (e.g., change preferred genre)
  - Clear memory per agent
- Agents notify users in chat when they learn something new (e.g., "SpotifyAgent now remembers you like Lofi")

### ✅ Memory Storage with Supabase (Self-hosted)
- Supabase runs in a Docker container
- Agent preferences and chat memory stored using Postgres
- Vector embeddings (via `pgvector`) used for long-term semantic recall

## 🔐 Security Model
- User login required
- Agent auth handled per request, based on user session
- Each agent performs tasks scoped to that user's access + tokens
- Admin controls which agents are available globally

## 🧑‍💼 Admin Control Features
- Agents are deployed and managed by admin
- Users only access agents made available by admin
- Agent tasks are routed securely via Abacus
- Role-based access (e.g. Free, Pro, Admin) planned

## ⚙️ Development Environment (Free-First)
- Entire project runs in Docker
- All agents use local LLMs via vLLM (preferred) or Ollama
- Supabase self-hosted locally for memory + task logging
- No external API costs during development
- Hosting planned after stable prototype

## Related Ideas
- [[AI Agent Architect System Prompt]] - Agent design methodology
- [[Building AI Agents with n8n]] - Alternative implementation platform
- [[AI's Role in Replacing Services and Personal Organization]] - Philosophical context
- [[Learning n8n]] - Technical implementation resource
- Multi-agent systems
- Distributed computing
- AI orchestration
