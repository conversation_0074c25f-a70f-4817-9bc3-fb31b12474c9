---
tags: [vLLM, Configuration, AI, LLM, Technical, Resource]
---

# vLLM Configuration Settings

## Core Command
```bash
vllm serve Orion-zhen/Qwen3-14B-AWQ --dtype float16 --gpu-memory-utilization 0.9 --swap-space 8 --max-model-len 4096 --max-num-seqs 2 --max-num-batched-tokens 512 --tensor-parallel-size 1 --block-size 16 --max-logprobs 5 --host 0.0.0.0 --port 7860 --served-model-name Qwen3-14B-AWQ
```

## Parameter Breakdown

### Model Configuration
- **Model**: `Orion-zhen/Qwen3-14B-AWQ`
- **Data Type**: `float16` (half precision for memory efficiency)
- **Served Model Name**: `Qwen3-14B-AWQ`

### Memory Management
- **GPU Memory Utilization**: `0.9` (90% of available GPU memory)
- **Swap Space**: `8` GB (additional memory for overflow)
- **Max Model Length**: `4096` tokens
- **Block Size**: `16` (memory allocation block size)

### Performance Settings
- **Max Sequences**: `2` (concurrent requests)
- **Max Batched Tokens**: `512` (tokens processed in batch)
- **Tensor Parallel Size**: `1` (single GPU setup)
- **Max Log Probabilities**: `5`

### Network Configuration
- **Host**: `0.0.0.0` (accessible from all network interfaces)
- **Port**: `7860` (service port)

## Use Cases
- Local AI model serving
- Development and testing
- Private AI inference
- Cost-effective AI deployment

## Related Ideas
- [[Abacus AI System]] - Could use vLLM for local model serving
- [[AI Agent Architect System Prompt]] - Local LLM implementation
- [[Building AI Agents with n8n]] - Backend AI processing
- [[Sports Betting AI Agent]] - Local inference for predictions
- Local AI deployment
- Model optimization
- Resource management
