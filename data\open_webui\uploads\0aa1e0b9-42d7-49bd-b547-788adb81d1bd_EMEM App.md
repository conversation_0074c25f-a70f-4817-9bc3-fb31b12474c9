---
tags: [Project, App, Development]
categories: [AI Projects Index, Voice & Audio Hub, Creative Projects Index, Technical Implementation Index]
---

# EMEM App Requirements Analysis

## App Overview
EMEM is a modern note-taking app that utilizes AI for structuring notes from voice recordings. The app has a sleek, minimalist design with a black, white, and purple color scheme.

## UI Components
Based on the provided mockups:

1. **Splash Screen**
- Full screen with centered EMEM logo on black background
- All app resources load in the background during this screen

2. **Home/Recording Screen**
- "Speak Your Mind" text with wave animation at the top
- Large, glowing purple button with waveform in the center
- But<PERSON> animates with incoming audio when recording
- Menu button in top-right corner

3. **Processing Screen**
- "Processing..." text
- Purple progress bar
- EMEM logo in top-left corner
- Menu button in top-right corner

4. **Note History Screen**
- Search bar at the top
- Notes organized by time periods (Yesterday, 5 Days ago, Last week, etc.)
- Each note displays its title
- EMEM logo in top-left corner
- Menu button in top-right corner

5. **Note Detail Screen**
- Note title at the top
- Tags displayed below title
- Content organized in sections (Key Themes, etc.)
- Navigation buttons at the bottom
- EMEM logo in top-left corner
- Menu button in top-right corner

6. **Settings Screen**
- Toggle for Light/Dark mode
- Various app settings options
- EMEM logo in top-left corner
- Menu button in top-right corner

## Core Functionality

1. **Audio Recording**
- Record user's voice when the purple button is pressed
- Display waveform animation during recording
- Save audio file for processing

2. **Transcription**
- Send audio to server at "http://1210morgan.zapto.org:8000/transcribe/"
- Server uses OpenAI Whisper large v3 turbo model for transcription
- Server also formats the text using AI (OpenRouter with Gemini model)

3. **Note Formatting**
- Notes follow the Yap Session template with sections:
- Session Highlights
- Key Quotes
- Questions and Reflections
- Related Topics
- Date and time are automatically added
- Tags are generated based on content

4. **Note Storage and Retrieval**
- Save formatted notes locally
- Organize notes by date
- Allow searching and filtering by tags

5. **Related Topics**
- AI finds connections between notes in user history
- Links related notes together

## Premium Features
- Custom note formatting templates
- Additional customization options
- Subscription-based model

## Technical Requirements
- Swift and SwiftUI for iOS development
- Integration with external transcription API
- Local database for note storage
- Authentication for premium features

## Related Ideas
- [[Emem Commercial Script]] - Marketing and branding
- [[AI Agent Obsidian Note]] - Related note automation
- [[Philosophy and Memory Recall]] - AI and thought organization
- [[Navigating Unnatural Feelings in AI Recording and Typing]] - User experience considerations
- Voice-to-text technology
- AI-powered note organization
- Mobile app development
