---
tags: [Learning, AI, n8n, Agents, Project, YouTube]
---

# Building AI Agents with n8n

*Learned: 2025-02-21 | Source: YouTube tutorials*

## Takeaway
- [x] Installed n8n with npx
- [x] cloned project with Git
- [ ] <!-- What's the core thing you learned? Keep it snappy. -->

## Details
- [x] created a n8n server that can be accessed anywhere with internet

## Source
- [n8n Ai Agent: Build a Self Learning Agent! (n8n tutorial) - YouTube](https://www.youtube.com/watch?v=g0IStM5DIp0&ab_channel=ProductiveDude) 
- [Building AI Agents in n8n Somehow Got Easier (as a beginner)](https://www.youtube.com/watch?v=TfqioNAP1W4&ab_channel=NateHerk%7CAIAutomation)

## Why It Matters
- Learn how to use n8n to build AI Agents
- Learning how to make AI Agents

## Next Steps
- Continue exploring n8n workflows
- Build first AI agent prototype

## Connections
- [[AI Agent Obsidian Note]] - Related AI automation project
- [[AI Auto Gmail Labler]] - Potential n8n implementation
- [[Learning n8n]] - Related learning resource
- [[AI Agent Architect System Prompt]] - Agent design principles
- [[AI Iterative Project Builder Agent]] - Related agent project

## Related Ideas
- [[AI's Role in Replacing Services and Personal Organization]] - Philosophical context
- Workflow automation
- AI agent architecture
- No-code/low-code development
