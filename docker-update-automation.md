# Docker Container Auto-Update Solutions

## 1. Watchtower (Recommended - Already Added)

Watchtower is now configured in your docker-compose.yaml and will:
- Check for updates daily at 4 AM
- Only update containers with the `watchtower.enable=true` label
- Clean up old images after updates
- Restart containers one by one to minimize downtime
- Send notifications (if configured)

### Key Features:
- **Schedule**: Runs daily at 4 AM (configurable via cron)
- **Selective Updates**: Only updates labeled containers
- **Rolling Restart**: Updates containers one at a time
- **Cleanup**: Removes old images automatically
- **Notifications**: Supports Slack, email, etc.

### Services Currently Enabled for Auto-Update:
- open-webui
- n8n  
- librechat

### To Enable Auto-Update for Other Services:
Add this label to any service you want auto-updated:
```yaml
labels:
  - "com.centurylinklabs.watchtower.enable=true"
```

### Environment Variables (Optional):
Add to your `.env` file:
```env
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

## 2. Alternative Solutions

### A. Ouroboros (Lightweight Alternative)
```yaml
ouroboros:
  image: pyouroboros/ouroboros:latest
  container_name: ouroboros
  environment:
    - CLEANUP=true
    - INTERVAL=300  # Check every 5 minutes
    - LOG_LEVEL=info
    - SELF_UPDATE=true
    - IGNORE=mongo postgres redis  # Don't update databases
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
  restart: unless-stopped
```

### B. Diun (Docker Image Update Notifier)
Only notifies about updates, doesn't auto-update:
```yaml
diun:
  image: crazymax/diun:latest
  container_name: diun
  environment:
    - TZ=America/Los_Angeles
    - LOG_LEVEL=info
    - DIUN_WATCH_WORKERS=20
    - DIUN_WATCH_SCHEDULE=0 */6 * * *  # Check every 6 hours
  volumes:
    - ./data/diun:/data
    - /var/run/docker.sock:/var/run/docker.sock:ro
  restart: unless-stopped
```

## 3. Manual Update Scripts

### PowerShell Script (Windows)
Create `update-containers.ps1`:
```powershell
# Services to update
$services = @("open-webui", "n8n", "librechat", "searxng")

foreach ($service in $services) {
    Write-Host "Updating $service..." -ForegroundColor Green
    
    # Stop the service
    docker-compose stop $service
    
    # Pull latest image
    docker-compose pull $service
    
    # Start the service
    docker-compose up -d $service
    
    Write-Host "$service updated successfully!" -ForegroundColor Green
}

# Clean up unused images
docker image prune -f
```

### Bash Script (Linux/WSL)
Create `update-containers.sh`:
```bash
#!/bin/bash
services=("open-webui" "n8n" "librechat" "searxng")

for service in "${services[@]}"; do
    echo "Updating $service..."
    docker-compose stop "$service"
    docker-compose pull "$service"
    docker-compose up -d "$service"
    echo "$service updated successfully!"
done

# Clean up unused images
docker image prune -f
```

## 4. CI/CD Integration (Advanced)

### GitHub Actions Workflow
Create `.github/workflows/update-containers.yml`:
```yaml
name: Update Docker Containers
on:
  schedule:
    - cron: '0 4 * * *'  # Daily at 4 AM
  workflow_dispatch:  # Manual trigger

jobs:
  update:
    runs-on: self-hosted  # Use your own runner
    steps:
      - uses: actions/checkout@v4
      - name: Update containers
        run: |
          docker-compose pull
          docker-compose up -d
          docker image prune -f
```

## 5. Best Practices

### Database Services (DO NOT Auto-Update)
Never auto-update these without manual intervention:
- MongoDB (abacus_chat_mongodb)
- PostgreSQL (librechat_vectordb)
- MariaDB (nextclouddb)
- Redis

### Services Safe for Auto-Update:
- open-webui
- n8n
- librechat
- searxng
- perplexica
- flowise
- crawl4ai

### Monitoring and Rollback:
1. **Health Checks**: All services have health checks configured
2. **Logs**: Monitor logs after updates: `docker-compose logs -f [service]`
3. **Rollback**: If issues occur, rollback with:
   ```bash
   docker-compose stop [service]
   docker tag [service]:latest [service]:backup
   docker pull [service]:[previous-version]
   docker-compose up -d [service]
   ```

## 6. Notification Setup

### Slack Notifications
1. Create a Slack webhook URL
2. Add to your `.env` file:
   ```env
   SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
   ```
3. Watchtower will send update notifications

### Email Notifications
Add to watchtower environment:
```yaml
environment:
  - WATCHTOWER_NOTIFICATIONS=email
  - WATCHTOWER_NOTIFICATION_EMAIL_FROM=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_TO=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER=smtp.gmail.com
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PORT=587
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_USER=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PASSWORD=your-app-password
```

## 7. Getting Started

1. **Start Watchtower**: `docker-compose up -d watchtower`
2. **Check logs**: `docker-compose logs -f watchtower`
3. **Test manually**: `docker exec watchtower watchtower --run-once`
4. **Monitor updates**: Check logs daily after 4 AM

Your setup is now configured for automatic updates with Watchtower!
